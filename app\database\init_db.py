"""
Database initialization script with TimescaleDB setup.
"""

from sqlalchemy import text
import logging

from app.database.connection import engine, init_database
from app.database.models import Base
from app.core.config import settings

logger = logging.getLogger(__name__)


def create_timescale_hypertables():
    """
    Create TimescaleDB hypertables for time-series data.
    """
    logger.info("Creating TimescaleDB hypertables...")
    
    try:
        with engine.connect() as conn:
            # Create hypertable for stock_ohlcv (1-minute data)
            conn.execute(text("""
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            """))
            
            # Create hypertable for stock_ohlcv_agg (aggregated data)
            conn.execute(text("""
                SELECT create_hypertable(
                    'stock_ohlcv_agg', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '7 days'
                );
            """))
            
            # Create hypertable for screener_results
            conn.execute(text("""
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            """))
            
            # Create hypertable for paper_trades
            conn.execute(text("""
                SELECT create_hypertable(
                    'paper_trades', 
                    'entry_time',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '30 days'
                );
            """))
            
            # Create hypertable for backtest_trades
            conn.execute(text("""
                SELECT create_hypertable(
                    'backtest_trades', 
                    'entry_time',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '30 days'
                );
            """))
            
            conn.commit()
            logger.info("TimescaleDB hypertables created successfully")
            
    except Exception as e:
        logger.error(f"Failed to create hypertables: {e}")
        raise


def create_indexes():
    """
    Create additional indexes for performance optimization.
    """
    logger.info("Creating additional indexes...")
    
    try:
        with engine.connect() as conn:
            # Additional indexes for stock_ohlcv
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            """))
            
            # Additional indexes for stock_ohlcv_agg
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_agg_symbol_timeframe_time_desc 
                ON stock_ohlcv_agg (symbol_id, timeframe, timestamp DESC);
            """))
            
            # Index for screener results
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_screener_results_symbol_strategy_time 
                ON screener_results (symbol_id, strategy_id, timestamp DESC);
            """))
            
            conn.commit()
            logger.info("Additional indexes created successfully")
            
    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")
        raise


def create_stored_procedures():
    """
    Create stored procedures for data aggregation and analysis.
    """
    logger.info("Creating stored procedures...")
    
    try:
        with engine.connect() as conn:
            # Procedure to aggregate 1-minute data to different timeframes
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION aggregate_ohlcv_data(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS VOID AS $$
                DECLARE
                    interval_minutes INTEGER;
                BEGIN
                    -- Determine interval in minutes
                    CASE p_timeframe
                        WHEN '5m' THEN interval_minutes := 5;
                        WHEN '10m' THEN interval_minutes := 10;
                        WHEN '15m' THEN interval_minutes := 15;
                        WHEN '30m' THEN interval_minutes := 30;
                        WHEN '1h' THEN interval_minutes := 60;
                        WHEN '1d' THEN interval_minutes := 1440;
                        ELSE RAISE EXCEPTION 'Invalid timeframe: %', p_timeframe;
                    END CASE;
                    
                    -- Insert aggregated data
                    INSERT INTO stock_ohlcv_agg (symbol_id, timeframe, timestamp, open, high, low, close, volume)
                    SELECT 
                        symbol_id,
                        p_timeframe,
                        time_bucket(INTERVAL '1 minute' * interval_minutes, timestamp) as bucket,
                        FIRST(open, timestamp) as open,
                        MAX(high) as high,
                        MIN(low) as low,
                        LAST(close, timestamp) as close,
                        SUM(volume) as volume
                    FROM stock_ohlcv
                    WHERE symbol_id = p_symbol_id
                        AND timestamp >= p_start_time
                        AND timestamp < p_end_time
                    GROUP BY symbol_id, bucket
                    ON CONFLICT (symbol_id, timeframe, timestamp) DO UPDATE SET
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume;
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            # Procedure to calculate technical indicators
            conn.execute(text("""
                CREATE OR REPLACE FUNCTION calculate_sma(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_period INTEGER,
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.timestamp,
                        AVG(t.close) OVER (
                            ORDER BY t.timestamp 
                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW
                        ) as sma
                    FROM stock_ohlcv_agg t
                    WHERE t.symbol_id = p_symbol_id
                        AND t.timeframe = p_timeframe
                        AND t.timestamp >= p_start_time
                        AND t.timestamp <= p_end_time
                    ORDER BY t.timestamp;
                END;
                $$ LANGUAGE plpgsql;
            """))
            
            conn.commit()
            logger.info("Stored procedures created successfully")
            
    except Exception as e:
        logger.error(f"Failed to create stored procedures: {e}")
        raise


def setup_database():
    """
    Complete database setup including tables, hypertables, indexes, and procedures.
    """
    logger.info("Starting complete database setup...")
    
    try:
        # Initialize database and create tables
        init_database()
        
        # Create TimescaleDB hypertables if enabled
        if settings.database.timescale_enabled:
            create_timescale_hypertables()
            create_indexes()
            create_stored_procedures()
        
        logger.info("Database setup completed successfully")
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        raise


if __name__ == "__main__":
    setup_database()
