2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.177208Z"}
2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.178217Z"}
2025-07-13 04:13:21,179 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.179218Z"}
2025-07-13 04:13:21,180 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.180227Z"}
2025-07-13 04:13:21,386 [INFO] app.database.connection: Database connection successful
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,388 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:13:21,388 [INFO] app.database.connection: Initializing database...
2025-07-13 04:13:21,392 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:13:21,679 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:13:21,679 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:13:21,709 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,710 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,711 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:43:21.711311Z"}
2025-07-13 04:14:15,839 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.839084Z"}
2025-07-13 04:14:15,840 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.840051Z"}
2025-07-13 04:14:15,841 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.841048Z"}
2025-07-13 04:14:15,842 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.842058Z"}
2025-07-13 04:14:16,028 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:16,028 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.028458Z"}
2025-07-13 04:14:16,029 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.029458Z"}
2025-07-13 04:14:16,030 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:16,030 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:16,033 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:16,061 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:16,062 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:16,068 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,069 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,070 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:16.070339Z"}
2025-07-13 04:14:44,669 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.669528Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,671 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.671525Z"}
2025-07-13 04:14:44,863 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.863536Z"}
2025-07-13 04:14:44,868 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.868536Z"}
2025-07-13 04:14:44,873 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.872545Z"}
2025-07-13 04:14:44,877 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.877533Z"}
2025-07-13 04:14:44,881 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.881535Z"}
2025-07-13 04:14:44,885 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.885549Z"}
2025-07-13 04:14:44,888 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.888542Z"}
2025-07-13 04:14:44,891 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.891536Z"}
2025-07-13 04:14:44,920 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.920101Z"}
2025-07-13 04:14:44,921 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.921111Z"}
2025-07-13 04:14:45,148 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.148443Z"}
2025-07-13 04:14:45,149 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.149440Z"}
2025-07-13 04:14:55,569 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.569862Z"}
2025-07-13 04:14:55,570 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.570829Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,767 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:55,768 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:55,769 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:55,773 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:55,797 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:55,797 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:55,823 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,824 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,825 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:55.825706Z"}
2025-07-13 04:15:42,336 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.336996Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,339 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.339279Z"}
2025-07-13 04:15:42,530 [INFO] app.database.connection: Database connection successful
2025-07-13 04:15:42,530 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.530331Z"}
2025-07-13 04:15:42,531 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.531333Z"}
2025-07-13 04:15:42,531 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:15:42,531 [INFO] app.database.connection: Initializing database...
2025-07-13 04:15:42,535 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:15:42,562 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:15:42,563 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:15:42,592 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:45:42.593657Z"}
