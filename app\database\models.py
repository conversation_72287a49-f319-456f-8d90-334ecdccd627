"""
Database models for the trading platform.
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, 
    Text, Index, ForeignKey, Enum, BigInteger, Numeric
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.database.connection import Base


class MarketType(str, enum.Enum):
    """Market type enumeration."""
    EQUITY = "EQUITY"
    INDEX = "INDEX"
    FUTURES = "FUTURES"
    OPTIONS = "OPTIONS"


class TradeType(str, enum.Enum):
    """Trade type enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class TradeStatus(str, enum.Enum):
    """Trade status enumeration."""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    CANCELLED = "CANCELLED"


class Symbol(Base):
    """Symbol master table."""
    __tablename__ = "symbols"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(50), unique=True, index=True, nullable=False)
    name = Column(String(200))
    market_type = Column(Enum(MarketType), nullable=False)
    exchange = Column(String(10), default="NSE")
    token = Column(String(50), unique=True, index=True)
    lot_size = Column(Integer, default=1)
    tick_size = Column(Float, default=0.05)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    ohlcv_data = relationship("StockOHLCV", back_populates="symbol_ref")
    aggregated_data = relationship("StockOHLCVAgg", back_populates="symbol_ref")


class StockOHLCV(Base):
    """1-minute OHLCV data table (TimescaleDB hypertable)."""
    __tablename__ = "stock_ohlcv"

    symbol_id = Column(Integer, ForeignKey("symbols.id"), nullable=False, primary_key=True)
    timestamp = Column(DateTime, nullable=False, primary_key=True)
    open = Column(Numeric(12, 4), nullable=False)
    high = Column(Numeric(12, 4), nullable=False)
    low = Column(Numeric(12, 4), nullable=False)
    close = Column(Numeric(12, 4), nullable=False)
    volume = Column(BigInteger, nullable=False)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    symbol_ref = relationship("Symbol", back_populates="ohlcv_data")

    # Composite index for efficient time-series queries
    __table_args__ = (
        Index('idx_stock_ohlcv_symbol_time', 'symbol_id', 'timestamp'),
    )


class StockOHLCVAgg(Base):
    """Aggregated OHLCV data for different timeframes."""
    __tablename__ = "stock_ohlcv_agg"

    symbol_id = Column(Integer, ForeignKey("symbols.id"), nullable=False, primary_key=True)
    timeframe = Column(String(10), nullable=False, primary_key=True)  # 5m, 15m, 30m, 1h, 1d
    timestamp = Column(DateTime, nullable=False, primary_key=True)
    open = Column(Numeric(12, 4), nullable=False)
    high = Column(Numeric(12, 4), nullable=False)
    low = Column(Numeric(12, 4), nullable=False)
    close = Column(Numeric(12, 4), nullable=False)
    volume = Column(BigInteger, nullable=False)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    symbol_ref = relationship("Symbol", back_populates="aggregated_data")

    # Composite index for efficient queries
    __table_args__ = (
        Index('idx_stock_ohlcv_agg_symbol_timeframe_time', 'symbol_id', 'timeframe', 'timestamp'),
    )


class Strategy(Base):
    """Strategy definitions table."""
    __tablename__ = "strategies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    parameters = Column(Text)  # JSON string of strategy parameters
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    screener_results = relationship("ScreenerResult", back_populates="strategy_ref")
    backtest_results = relationship("BacktestResult", back_populates="strategy_ref")


class ScreenerResult(Base):
    """Symbol screener results table."""
    __tablename__ = "screener_results"

    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, primary_key=True)
    symbol_id = Column(Integer, ForeignKey("symbols.id"), nullable=False, primary_key=True)
    timestamp = Column(DateTime, nullable=False, primary_key=True)
    score = Column(Float)
    extra_data = Column(Text)  # JSON string with additional data
    created_at = Column(DateTime, default=func.now())

    # Relationships
    strategy_ref = relationship("Strategy", back_populates="screener_results")
    symbol_ref = relationship("Symbol")

    # Composite index
    __table_args__ = (
        Index('idx_screener_results_strategy_time', 'strategy_id', 'timestamp'),
    )


class BacktestResult(Base):
    """Backtest results table."""
    __tablename__ = "backtest_results"

    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False)
    symbol_id = Column(Integer, ForeignKey("symbols.id"), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_capital = Column(Numeric(15, 2), nullable=False)
    final_capital = Column(Numeric(15, 2), nullable=False)
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float)
    profit_factor = Column(Float)
    max_drawdown = Column(Float)
    roi = Column(Float)
    risk_reward_ratio = Column(Float)
    extra_data = Column(Text)  # JSON string with detailed results
    created_at = Column(DateTime, default=func.now())

    # Relationships
    strategy_ref = relationship("Strategy", back_populates="backtest_results")
    symbol_ref = relationship("Symbol")
    trades = relationship("BacktestTrade", back_populates="backtest_ref")


class BacktestTrade(Base):
    """Individual trades from backtesting."""
    __tablename__ = "backtest_trades"

    id = Column(BigInteger, primary_key=True, index=True)
    backtest_id = Column(Integer, ForeignKey("backtest_results.id"), nullable=False)
    entry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime)
    trade_type = Column(Enum(TradeType), nullable=False)
    entry_price = Column(Numeric(12, 4), nullable=False)
    exit_price = Column(Numeric(12, 4))
    quantity = Column(Integer, nullable=False)
    pnl = Column(Numeric(15, 2))
    status = Column(Enum(TradeStatus), default=TradeStatus.OPEN)

    # Relationships
    backtest_ref = relationship("BacktestResult", back_populates="trades")


class PaperTrade(Base):
    """Paper trading records."""
    __tablename__ = "paper_trades"

    id = Column(BigInteger, primary_key=True, index=True)
    symbol_id = Column(Integer, ForeignKey("symbols.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("strategies.id"))
    entry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime)
    trade_type = Column(Enum(TradeType), nullable=False)
    entry_price = Column(Numeric(12, 4), nullable=False)
    exit_price = Column(Numeric(12, 4))
    quantity = Column(Integer, nullable=False)
    pnl = Column(Numeric(15, 2))
    status = Column(Enum(TradeStatus), default=TradeStatus.OPEN)
    extra_data = Column(Text)  # JSON string with additional trade data
    created_at = Column(DateTime, default=func.now())

    # Relationships
    symbol_ref = relationship("Symbol")
    strategy_ref = relationship("Strategy")

    # Index for efficient queries
    __table_args__ = (
        Index('idx_paper_trades_symbol_time', 'symbol_id', 'entry_time'),
        Index('idx_paper_trades_status', 'status'),
    )
